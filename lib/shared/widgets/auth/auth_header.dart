import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';

class AuthHeader extends StatelessWidget {
  final String? title;
  final String? subtitle;
  final double? height;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final bool showLanguageButton;

  const AuthHeader({
    super.key,
    this.title,
    this.subtitle,
    this.height,
    this.showBackButton = true,
    this.onBackPressed,
    this.showLanguageButton = true,
  });

  @override
  Widget build(BuildContext context) {
    final headerHeight = height ?? (MediaQuery.of(context).size.height * 0.36);
    
    return Container(
      width: double.infinity,
      height: headerHeight,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            context.theme.primaryColor,
            context.theme.primaryColor.withOpacity(0.8),
          ],
        ),
      ),
      child: Stack(
        children: [
          // Top navigation bar
          Positioned(
            top: 50.gh,
            left: 0,
            right: 0,
            child: Row(
              children: [
                // Back button
                if (showBackButton) ...[
                  IconButton(
                    onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.arrow_back, color: Colors.white),
                  ),
                ] else ...[
                  SizedBox(width: 48.gw),
                ],
                
                // Centered Logo/Title
                Expanded(
                  child: Center(
                    child: Text(
                      'WD',
                      style: TextStyle(
                        fontSize: 24.gw,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),

                // Language button placeholder
                if (showLanguageButton) ...[
                  IconButton(
                    onPressed: () {
                      // Language menu functionality can be added later
                    },
                    icon: const Icon(Icons.language, color: Colors.white),
                  ),
                ] else ...[
                  SizedBox(width: 48.gw),
                ],
              ],
            ),
          ),

          // Title and subtitle section
          if (title != null || subtitle != null) ...[
            Positioned(
              bottom: 40.gh,
              left: 0,
              right: 0,
              child: Column(
                children: [
                  if (title != null) ...[
                    Text(
                      title!,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 28.gw,
                        fontWeight: FontWeight.w400,
                        color: Colors.white,
                        height: 1.2,
                      ),
                    ),
                  ],
                  if (subtitle != null) ...[
                    SizedBox(height: 4.gh),
                    Text(
                      subtitle!,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 28.gw,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        height: 1.2,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
