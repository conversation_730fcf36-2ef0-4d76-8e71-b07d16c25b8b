class Country {
  final String name;
  final String code;
  final int areaCode;
  final String flag;

  const Country({
    required this.name,
    required this.code,
    required this.areaCode,
    required this.flag,
  });

  factory Country.fromJson(Map<String, dynamic> json) {
    return Country(
      name: json['name'] as String,
      code: json['code'] as String,
      areaCode: json['areaCode'] as int,
      flag: json['flag'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'code': code,
      'areaCode': areaCode,
      'flag': flag,
    };
  }

  String get displayText => '$flag +$areaCode';
  String get fullDisplayText => '$flag $name (+$areaCode)';

  /// Generate emoji flag from country code
  static String emojiFlag(String countryCode) {
    return countryCode.toUpperCase().runes.map((codeUnit) => String.fromCharCode(0x1F1E6 + (codeUnit - 0x41))).join();
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Country &&
        other.name == name &&
        other.code == code &&
        other.areaCode == areaCode &&
        other.flag == flag;
  }

  @override
  int get hashCode {
    return name.hashCode ^ code.hashCode ^ areaCode.hashCode ^ flag.hashCode;
  }

  @override
  String toString() {
    return 'Country(name: $name, code: $code, areaCode: $areaCode, flag: $flag)';
  }
}
