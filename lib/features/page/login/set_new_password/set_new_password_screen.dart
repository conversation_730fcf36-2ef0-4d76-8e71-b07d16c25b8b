import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/shared/widgets/animation/scale_animation.dart';
import 'package:wd/shared/widgets/auth/auth_tab_bar.dart';
import 'package:wd/shared/widgets/text_fields/phone_input_field.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/common_textfield.dart';
import 'package:wd/core/models/country.dart';
import 'package:wd/core/services/country_service.dart';

import '../../../../core/utils/system_util.dart';
import '../forgot/forgot_cubit.dart';

class SetNewPasswordPage extends StatefulWidget {
  final LoginType resetType;
  final String contactInfo; // phone or email

  const SetNewPasswordPage({
    super.key,
    required this.resetType,
    required this.contactInfo,
  });

  @override
  State<SetNewPasswordPage> createState() => _SetNewPasswordPageState();
}

class _SetNewPasswordPageState extends State<SetNewPasswordPage> {
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  Country? _selectedCountry;

  static const double _verticalSpacing = 15.0;
  static const double _sectionSpacing = 30.0;

  @override
  void initState() {
    super.initState();
    _initializeCountry();
  }

  Future<void> _initializeCountry() async {
    final defaultCountry = await CountryService.instance.getDefaultCountry();
    if (mounted) {
      setState(() {
        _selectedCountry = defaultCountry;
      });
    }
  }

  String emojiFlag(String countryCode) {
    return countryCode.toUpperCase().runes.map((codeUnit) => String.fromCharCode(0x1F1E6 + (codeUnit - 0x41))).join();
  }

  @override
  Widget build(BuildContext context) {
    return _buildSliverLayout();
  }

  /// Builds the sliver layout with persistent header matching login design
  Widget _buildSliverLayout() {
    final screenHeight = MediaQuery.of(context).size.height;
    final headerHeight = screenHeight * 0.36; // ~400px on 1252px screen - same as login

    return CustomScrollView(
      slivers: [
        // Header section with title
        SliverPersistentHeader(
          pinned: false,
          floating: false,
          delegate: _SetPasswordHeaderDelegate(
            minHeight: headerHeight * 0.6, // Minimum height when collapsed
            maxHeight: headerHeight, // Full height when expanded
            title: _buildTitle(),
            subtitle: _buildSubtitle(),
          ),
        ),
        // Content section
        SliverToBoxAdapter(
          child: Container(
            decoration: BoxDecoration(
              color: context.theme.scaffoldBackgroundColor,
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 40.gw),
              child: Column(
                children: [
                  SizedBox(height: 20.gw),
                  // Tab bar for phone/email selection
                  PhoneEmailTabBar(
                    selectedIndex: widget.resetType == LoginType.phone ? 0 : 1,
                    onTabChanged: (index) {
                      // Tab switching is handled by parent widget
                    },
                  ),
                  SizedBox(height: _sectionSpacing.gw),
                  // Form content
                  BlocBuilder<ForgotCubit, ForgotState>(
                    builder: (context, state) {
                      return CommonScaleAnimationWidget(
                        children: [
                          if (widget.resetType == LoginType.phone) ...[
                            _buildPhoneInputField(),
                          ] else ...[
                            _buildEmailInputField(),
                          ],
                          SizedBox(height: _verticalSpacing.gw),
                          _buildVerificationCodeInputField(),
                          SizedBox(height: _verticalSpacing.gw),
                          _buildPasswordInput(),
                          SizedBox(height: _verticalSpacing.gw),
                          _buildConfirmPasswordInput(),
                          SizedBox(height: _sectionSpacing.gw),
                          _buildSetPasswordButton(),
                          SizedBox(height: 20.gw),
                          _buildBottomText(),
                          SizedBox(height: 40.gw), // Extra bottom padding
                        ],
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Builds the title for the header
  Widget _buildTitle() {
    return Text(
      'Set New',
      textAlign: TextAlign.center,
      style: TextStyle(
        fontSize: 28.gw,
        fontWeight: FontWeight.w400,
        color: Colors.white,
        height: 1.2,
      ),
    );
  }

  /// Builds the subtitle for the header
  Widget _buildSubtitle() {
    String subtitle;

    switch (widget.resetType) {
      case LoginType.phone:
        subtitle = 'Password via Phone';
        break;
      case LoginType.email:
        subtitle = 'Password via Email';
        break;
      default:
        subtitle = 'Password';
    }

    return Text(
      subtitle,
      textAlign: TextAlign.center,
      style: TextStyle(
        fontSize: 28.gw,
        fontWeight: FontWeight.bold,
        color: Colors.white,
        height: 1.2,
      ),
    );
  }

  /// Builds the phone input field with country code dropdown
  Widget _buildPhoneInputField() {
    return PhoneInputField(
      controller: TextEditingController()..text = widget.contactInfo,
      hintText: "hint_enter_phone".tr(),
      selectedCountry: _selectedCountry,
      enabled: false, // Read-only since it's already verified
      onCountryChanged: (country) {
        setState(() {
          _selectedCountry = country;
        });
      },
    );
  }

  /// Builds the email input field
  Widget _buildEmailInputField() {
    return CommonTextField(
      controller: TextEditingController()..text = widget.contactInfo,
      hintText: "Enter email",
      prefixIcon: Image.asset(Assets.iconEmail, width: 20.gw, height: 20.gw),
      inputEnable: false, // Read-only since it's already verified
    );
  }

  /// Builds the verification code input field
  Widget _buildVerificationCodeInputField() {
    return CommonTextField(
      controller: TextEditingController(),
      hintText: "Please enter the code",
      prefixIcon: Image.asset(Assets.iconLoginShield, width: 20.gw, height: 20.gw),
      suffixIcon: _buildGetCodeButton(),
    );
  }

  /// Builds the password input field
  Widget _buildPasswordInput() {
    return CommonTextField(
      controller: TextEditingController(),
      hintText: "Password must be 6—22 letters or digits",
      prefixIcon: Image.asset(Assets.iconLoginPassword, width: 20.gw, height: 20.gw),
      obscureText: !_isPasswordVisible,
      suffixIcon: IconButton(
        icon: Icon(_isPasswordVisible ? Icons.visibility : Icons.visibility_off, color: Colors.white),
        onPressed: () => setState(() => _isPasswordVisible = !_isPasswordVisible),
      ),
    );
  }

  /// Builds the confirm password input field
  Widget _buildConfirmPasswordInput() {
    return CommonTextField(
      controller: TextEditingController(),
      hintText: "Confirm Password",
      prefixIcon: Image.asset(Assets.iconLoginPassword, width: 20.gw, height: 20.gw),
      obscureText: !_isConfirmPasswordVisible,
      suffixIcon: IconButton(
        icon: Icon(_isConfirmPasswordVisible ? Icons.visibility : Icons.visibility_off, color: Colors.white),
        onPressed: () => setState(() => _isConfirmPasswordVisible = !_isConfirmPasswordVisible),
      ),
    );
  }

  /// Builds the get code button
  Widget _buildGetCodeButton() {
    return Container(
      margin: EdgeInsets.all(8.gw),
      child: CommonButton(
        title: "Get code",
        width: 80.gw,
        height: 32.gw,
        textColor: Colors.black,
        backgroundColor: context.theme.primaryColor,
        onPressed: () {
          // TODO: Implement get code functionality for set new password
        },
      ),
    );
  }

  /// Builds the set password button
  Widget _buildSetPasswordButton() {
    return CommonButton(
      title: "Log In",
      textColor: context.colorTheme.btnTitlePrimary,
      onPressed: () {
        // TODO: Implement set new password functionality
      },
    );
  }

  /// Builds the bottom text
  Widget _buildBottomText() {
    return GestureDetector(
      onTap: () => SystemUtil.contactService(),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.gw),
        child: RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            style: TextStyle(
              fontSize: 12.fs,
              color: Colors.grey,
            ),
            children: [
              const TextSpan(
                  text:
                      '*Only users with a bound mobile number or email can retrieve their password via self-service. Users without a bound number should contact '),
              TextSpan(
                text: 'Online Support',
                style: TextStyle(
                  color: context.theme.primaryColor,
                  decoration: TextDecoration.underline,
                ),
              ),
              const TextSpan(text: ' for assistance'),
            ],
          ),
        ),
      ),
    );
  }
}

/// Custom SliverPersistentHeaderDelegate for the set new password header
class _SetPasswordHeaderDelegate extends SliverPersistentHeaderDelegate {
  final double minHeight;
  final double maxHeight;
  final Widget? title;
  final Widget? subtitle;

  _SetPasswordHeaderDelegate({
    required this.minHeight,
    required this.maxHeight,
    this.title,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    final progress = (shrinkOffset / (maxHeight - minHeight)).clamp(0.0, 1.0);
    final opacity = 1.0 - progress;

    return Container(
      width: double.infinity,
      height: maxHeight,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            context.theme.primaryColor,
            context.theme.primaryColor.withOpacity(0.8),
          ],
        ),
      ),
      child: Stack(
        children: [
          // Top navigation bar
          Positioned(
            top: 50.gw,
            left: 0,
            right: 0,
            child: Row(
              children: [
                // Back button
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.arrow_back, color: Colors.white),
                ),

                // Centered Logo/Title
                Expanded(
                  child: Center(
                    child: Text(
                      'WD',
                      style: TextStyle(
                        fontSize: 24.gw,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),

                // Placeholder for symmetry
                SizedBox(width: 48.gw),
              ],
            ),
          ),

          // Title and subtitle section
          if (title != null || subtitle != null) ...[
            Positioned(
              bottom: 40.gw,
              left: 0,
              right: 0,
              child: Opacity(
                opacity: opacity,
                child: Column(
                  children: [
                    if (title != null) title!,
                    if (subtitle != null) ...[
                      SizedBox(height: 4.gh),
                      subtitle!,
                    ],
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  @override
  double get maxExtent => maxHeight;

  @override
  double get minExtent => minHeight;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return oldDelegate is! _SetPasswordHeaderDelegate ||
        oldDelegate.minHeight != minHeight ||
        oldDelegate.maxHeight != maxHeight ||
        oldDelegate.title != title ||
        oldDelegate.subtitle != subtitle;
  }
}
