import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/shared/widgets/animation/scale_animation.dart';
import 'package:wd/shared/widgets/auth/auth_tab_bar.dart';
import 'package:wd/shared/widgets/text_fields/phone_input_field.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/common_textfield.dart';
import 'package:wd/core/models/country.dart';
import 'package:wd/core/services/country_service.dart';

import '../../../../core/utils/system_util.dart';
import 'forgot_cubit.dart';

class ForgotPage extends StatefulWidget {
  const ForgotPage({super.key});

  @override
  State<ForgotPage> createState() => _ForgotPageState();
}

class _ForgotPageState extends State<ForgotPage> {
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  Country? _selectedCountry;

  static const double _verticalSpacing = 15.0;
  static const double _sectionSpacing = 30.0;

  @override
  void initState() {
    super.initState();
    _initializeCountry();
  }

  Future<void> _initializeCountry() async {
    final defaultCountry = await CountryService.instance.getDefaultCountry();
    if (mounted) {
      setState(() {
        _selectedCountry = defaultCountry;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return _buildSliverLayout();
  }

  /// Builds the sliver layout with persistent header matching login design
  Widget _buildSliverLayout() {
    final screenHeight = MediaQuery.of(context).size.height;
    final headerHeight = screenHeight * 0.36; // ~400px on 1252px screen - same as login

    return CustomScrollView(
      slivers: [
        // Header section with title
        SliverPersistentHeader(
          pinned: false,
          floating: false,
          delegate: _ForgotHeaderDelegate(
            minHeight: headerHeight * 0.6, // Minimum height when collapsed
            maxHeight: headerHeight, // Full height when expanded
            title: _buildTitle(),
            subtitle: _buildSubtitle(),
          ),
        ),
        // Content section
        SliverToBoxAdapter(
          child: Container(
            decoration: BoxDecoration(
              color: context.theme.scaffoldBackgroundColor,
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 40.gw),
              child: Column(
                children: [
                  SizedBox(height: 20.gw),
                  // Tab bar for phone/email selection
                  BlocBuilder<ForgotCubit, ForgotState>(
                    builder: (context, state) {
                      return PhoneEmailTabBar(
                        selectedIndex: state.forgotType == LoginType.phone ? 0 : 1,
                        onTabChanged: (index) {
                          final newType = index == 0 ? LoginType.phone : LoginType.email;
                          context.read<ForgotCubit>().updateForgotType(newType);
                        },
                      );
                    },
                  ),
                  SizedBox(height: _sectionSpacing.gw),
                  // Form content
                  BlocBuilder<ForgotCubit, ForgotState>(
                    builder: (context, state) {
                      return CommonScaleAnimationWidget(
                        children: [
                          if (state.forgotType == LoginType.phone) ...[
                            _buildPhoneInputField(state),
                          ] else ...[
                            _buildEmailInputField(state),
                          ],
                          SizedBox(height: _verticalSpacing.gw),
                          _buildVerificationCodeInputField(state),
                          SizedBox(height: _verticalSpacing.gw),
                          _buildPasswordInput(state),
                          SizedBox(height: _verticalSpacing.gw),
                          _buildConfirmPasswordInput(state),
                          SizedBox(height: _sectionSpacing.gw),
                          _buildResetButton(),
                          SizedBox(height: 20.gw),
                          _buildBottomText(),
                          SizedBox(height: 40.gw), // Extra bottom padding
                        ],
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Builds the title for the header
  Widget _buildTitle() {
    return BlocBuilder<ForgotCubit, ForgotState>(
      builder: (context, state) {
        return Text(
          'Reset',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 28.gw,
            fontWeight: FontWeight.w400,
            color: Colors.white,
            height: 1.2,
          ),
        );
      },
    );
  }

  /// Builds the subtitle for the header
  Widget _buildSubtitle() {
    return BlocBuilder<ForgotCubit, ForgotState>(
      builder: (context, state) {
        final forgotType = state.forgotType;
        String subtitle;

        switch (forgotType) {
          case LoginType.phone:
            subtitle = 'Password via Phone';
            break;
          case LoginType.email:
            subtitle = 'Password via Email';
            break;
          default:
            subtitle = 'Password';
        }

        return Text(
          subtitle,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 28.gw,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            height: 1.2,
          ),
        );
      },
    );
  }

  /// Builds the title section
  Widget _buildTitleSection() {
    return Column(
      children: [
        Text(
          'Forgot your',
          textAlign: TextAlign.center,
          style: context.textTheme.primary.copyWith(
            fontSize: 28.gw,
            fontWeight: FontWeight.w400,
            color: Colors.white,
            height: 1.2,
          ),
        ),
        Text(
          'Password',
          textAlign: TextAlign.center,
          style: context.textTheme.primary.copyWith(
            fontSize: 28.gw,
            fontWeight: FontWeight.bold,
            color: context.theme.primaryColor,
            height: 1.2,
          ),
        ),
      ],
    );
  }

  /// Builds the phone input field with country code dropdown
  Widget _buildPhoneInputField(ForgotState state) {
    return PhoneInputField(
      controller: state.phoneController,
      hintText: "hint_enter_phone".tr(),
      selectedCountry: _selectedCountry,
      onCountryChanged: (country) {
        setState(() {
          _selectedCountry = country;
        });
      },
      onChanged: (value) => context.read<ForgotCubit>().setPhone(value),
    );
  }

  /// Builds the email input field
  Widget _buildEmailInputField(ForgotState state) {
    return CommonTextField(
      controller: state.emailController,
      hintText: "Enter email",
      prefixIcon: const Icon(Icons.email_outlined, color: Colors.white),
      onChanged: (value) => context.read<ForgotCubit>().setEmail(value),
    );
  }

  /// Builds the verification code input field
  Widget _buildVerificationCodeInputField(ForgotState state) {
    return CommonTextField(
      controller: state.smsCodeController,
      hintText: "Please enter the code",
      prefixIcon: const Icon(Icons.shield_outlined, color: Colors.white),
      suffixIcon: _buildGetCodeButton(state),
      onChanged: (value) => context.read<ForgotCubit>().setSmsCode(value),
    );
  }

  /// Builds the password input field
  Widget _buildPasswordInput(ForgotState state) {
    return CommonTextField(
      controller: TextEditingController(),
      hintText: "Password must be 6—22 letters or digits",
      prefixIcon: const Icon(Icons.lock_outline, color: Colors.white),
      obscureText: !_isPasswordVisible,
      suffixIcon: IconButton(
        icon: Icon(_isPasswordVisible ? Icons.visibility : Icons.visibility_off, color: Colors.white),
        onPressed: () => setState(() => _isPasswordVisible = !_isPasswordVisible),
      ),
      onChanged: (value) => context.read<ForgotCubit>().setPassword(value),
    );
  }

  /// Builds the confirm password input field
  Widget _buildConfirmPasswordInput(ForgotState state) {
    return CommonTextField(
      controller: TextEditingController(),
      hintText: "Confirm Password",
      prefixIcon: const Icon(Icons.lock_outline, color: Colors.white),
      obscureText: !_isConfirmPasswordVisible,
      suffixIcon: IconButton(
        icon: Icon(_isConfirmPasswordVisible ? Icons.visibility : Icons.visibility_off, color: Colors.white),
        onPressed: () => setState(() => _isConfirmPasswordVisible = !_isConfirmPasswordVisible),
      ),
      onChanged: (value) => context.read<ForgotCubit>().setConfirmPassword(value),
    );
  }

  /// Builds the get code button
  Widget _buildGetCodeButton(ForgotState state) {
    return Container(
      margin: EdgeInsets.all(8.gw),
      child: CommonButton(
        title: "Get code",
        width: 80.gw,
        height: 32.gw,
        textColor: Colors.black,
        backgroundColor: context.theme.primaryColor,
        onPressed: () {
          // TODO: Implement get code functionality
          if (state.forgotType == LoginType.phone) {
            _sendPhoneVerificationCode(state.phone);
          } else {
            _sendEmailVerificationCode(state.email);
          }
        },
      ),
    );
  }

  /// Builds the reset button
  Widget _buildResetButton() {
    return BlocBuilder<ForgotCubit, ForgotState>(
      builder: (context, state) {
        return CommonButton(
          title: "Log In",
          textColor: context.colorTheme.btnTitlePrimary,
          showLoading: state.forgotStatus == SimplyNetStatus.loading,
          onPressed: () => context.read<ForgotCubit>().resetPassword(),
        );
      },
    );
  }

  /// Sends phone verification code
  void _sendPhoneVerificationCode(String phone) {
    if (phone.isEmpty) {
      // Show error message
      return;
    }

    // TODO: Implement phone verification API call
    // Use the existing phone verification API with area code
    // API: /sms/userBindPhoneNo
    // Body: {"phoneNo": "${_selectedCountry?.areaCode}$phone"}
  }

  /// Sends email verification code
  void _sendEmailVerificationCode(String email) {
    if (email.isEmpty) {
      // Show error message
      return;
    }

    // TODO: Implement email verification API call
    // API: /mail/userBindMail
    // Body: {"mail": email, "mailCode": "verification_code"}
  }

  /// Builds the bottom text
  Widget _buildBottomText() {
    return GestureDetector(
      onTap: () => SystemUtil.contactService(),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.gw),
        child: RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            style: TextStyle(
              fontSize: 12.fs,
              color: Colors.grey,
            ),
            children: [
              const TextSpan(
                  text:
                      '*Only users with a bound mobile number or email can retrieve their password via self-service. Users without a bound number should contact '),
              TextSpan(
                text: 'Online Support',
                style: TextStyle(
                  color: context.theme.primaryColor,
                  decoration: TextDecoration.underline,
                ),
              ),
              const TextSpan(text: ' for assistance'),
            ],
          ),
        ),
      ),
    );
  }
}

/// Custom SliverPersistentHeaderDelegate for the forgot password header
class _ForgotHeaderDelegate extends SliverPersistentHeaderDelegate {
  final double minHeight;
  final double maxHeight;
  final Widget? title;
  final Widget? subtitle;

  _ForgotHeaderDelegate({
    required this.minHeight,
    required this.maxHeight,
    this.title,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    final progress = (shrinkOffset / (maxHeight - minHeight)).clamp(0.0, 1.0);
    final opacity = 1.0 - progress;

    return Container(
      width: double.infinity,
      height: maxHeight,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            context.theme.primaryColor,
            context.theme.primaryColor.withOpacity(0.8),
          ],
        ),
      ),
      child: Stack(
        children: [
          // Top navigation bar
          Positioned(
            top: 50.gw,
            left: 0,
            right: 0,
            child: Row(
              children: [
                // Back button
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.arrow_back, color: Colors.white),
                ),

                // Centered Logo/Title
                Expanded(
                  child: Center(
                    child: Text(
                      'WD',
                      style: TextStyle(
                        fontSize: 24.gw,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),

                // Placeholder for symmetry
                SizedBox(width: 48.gw),
              ],
            ),
          ),

          // Title and subtitle section
          if (title != null || subtitle != null) ...[
            Positioned(
              bottom: 40.gw,
              left: 0,
              right: 0,
              child: Opacity(
                opacity: opacity,
                child: Column(
                  children: [
                    if (title != null) title!,
                    if (subtitle != null) ...[
                      SizedBox(height: 4.gh),
                      subtitle!,
                    ],
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  @override
  double get maxExtent => maxHeight;

  @override
  double get minExtent => minHeight;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return oldDelegate is! _ForgotHeaderDelegate ||
        oldDelegate.minHeight != minHeight ||
        oldDelegate.maxHeight != maxHeight ||
        oldDelegate.title != title ||
        oldDelegate.subtitle != subtitle;
  }
}
